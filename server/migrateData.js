const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');
require('dotenv').config({ path: path.resolve(__dirname, '../.env') });

const supabaseUrl = process.env.SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_KEY;
const supabase = createClient(supabaseUrl, supabaseKey);

async function migrateData() {
  try {
    // Insert prompts data
    const promptsPath = path.join(__dirname, './data/prompts.json');
    const promptsData = JSON.parse(fs.readFileSync(promptsPath, 'utf8'));
    const { data: prompts, error: promptsError } = await supabase
      .from('prompts')
      .insert(promptsData.prompts);

    if (promptsError) {
      console.error('Error inserting prompts:', promptsError);
    } else {
      console.log('Prompts inserted successfully:', prompts);
    }

    // Insert evaluations data
    const evaluationsPath = path.join(__dirname, './data/evaluations.json');
    const evaluationsData = JSON.parse(fs.readFileSync(evaluationsPath, 'utf8'));
    const { data: evaluations, error: evaluationsError } = await supabase
      .from('evaluations')
      .insert(evaluationsData.evaluations);

    if (evaluationsError) {
      console.error('Error inserting evaluations:', evaluationsError);
    } else {
      console.log('Evaluations inserted successfully:', evaluations);
    }

  } catch (error) {
    console.error('Migration failed:', error);
  }
}

migrateData();